body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: none;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

.btn {
    border-radius: 4px;
    font-weight: 500;
}

.form-control {
    border-radius: 4px;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

.badge {
    font-size: 0.9rem;
    padding: 0.35em 0.65em;
}

canvas {
    max-width: 100%;
}

/* Status colors */
.bg-success {
    background-color: #198754 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

.bg-info {
    background-color: #0dcaf0 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-dark {
    background-color: #212529 !important;
}

/* Price flash animation */
@keyframes price-flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(13, 110, 253, 0.2); }
    100% { background-color: transparent; }
}

.price-flash {
    animation: price-flash 1s ease;
    border-radius: 3px;
    padding: 2px 4px;
}

/* Refresh button styles */
#refresh-prices {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
}
