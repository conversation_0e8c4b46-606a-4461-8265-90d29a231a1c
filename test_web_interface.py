import requests
import time
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_web_interface():
    """Test the web interface functionality."""
    base_url = "http://localhost:5000"
    
    try:
        # Test 1: Check if the web interface is running
        logger.info("Testing web interface connection...")
        response = requests.get(base_url)
        if response.status_code == 200:
            logger.info("Web interface is running and accessible.")
        else:
            logger.error(f"Failed to connect to web interface. Status code: {response.status_code}")
            return False
        
        # Test 2: Check the status endpoint
        logger.info("Testing status endpoint...")
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status_data = response.json()
            logger.info(f"Status endpoint is working. Current bot status: {status_data.get('status', 'Unknown')}")
        else:
            logger.error(f"Failed to access status endpoint. Status code: {response.status_code}")
            return False
        
        logger.info("All tests passed successfully!")
        return True
    
    except requests.exceptions.ConnectionError:
        logger.error("Connection error. Make sure the web interface is running.")
        return False
    except Exception as e:
        logger.error(f"Error testing web interface: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting web interface test...")
    
    # Check if the web interface is already running
    try:
        response = requests.get("http://localhost:5000")
        logger.info("Web interface is already running.")
    except:
        logger.info("Web interface is not running. Please start it with 'python app.py' in another terminal.")
        logger.info("Waiting for 10 seconds to give you time to start the web interface...")
        time.sleep(10)
    
    # Run the tests
    if test_web_interface():
        logger.info("Web interface is working correctly!")
        sys.exit(0)
    else:
        logger.error("Web interface test failed.")
        sys.exit(1)
