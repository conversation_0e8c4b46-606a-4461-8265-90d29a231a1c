import ccxt
import os
from dotenv import load_dotenv
import time
import logging
import pandas as pd
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default trading parameters
DEFAULT_SYMBOL = 'SOL/USDT'
DEFAULT_AMOUNT = 1.0  # Amount of SOL to trade
DEFAULT_BINANCE_FEE = 0.001  # 0.1% trading fee
DEFAULT_KRAKEN_FEE = 0.002  # 0.2% trading fee
DEFAULT_MIN_PROFIT_MARGIN = 0.003  # 0.3% minimum profit margin

# Import enhanced strategy
try:
    from enhanced_strategy import run_enhanced_strategy
    ENHANCED_STRATEGY_AVAILABLE = True
except ImportError:
    logger.warning("Enhanced strategy module not found. Using basic strategy.")
    ENHANCED_STRATEGY_AVAILABLE = False

def initialize_exchanges(binance_api_key, binance_api_secret, kraken_api_key, kraken_api_secret):
    """Initialize exchange connections with API keys."""
    binance = ccxt.binance({
        'apiKey': binance_api_key,
        'secret': binance_api_secret,
        'enableRateLimit': True,
    })

    kraken = ccxt.kraken({
        'apiKey': kraken_api_key,
        'secret': kraken_api_secret,
        'enableRateLimit': True,
    })

    return binance, kraken

def get_prices(binance, kraken, symbol=DEFAULT_SYMBOL):
    """Fetch detailed prices and market data from Binance and Kraken."""
    try:
        # Get current timestamp
        timestamp = int(time.time() * 1000)
        human_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp/1000))

        # Initialize price data structure
        prices = {
            'timestamp': timestamp,
            'human_time': human_time,
            'binance': {
                'bid': None,
                'ask': None,
                'last': None,
                'volume': None,
                'high': None,
                'low': None,
                'vwap': None
            },
            'kraken': {
                'bid': None,
                'ask': None,
                'last': None,
                'volume': None,
                'high': None,
                'low': None,
                'vwap': None
            }
        }

        # Verify the symbol is available on both exchanges
        logger.info(f"Fetching prices for {symbol}")

        # Get Binance ticker data
        try:
            # Load markets first to ensure the symbol exists
            binance_markets = binance.load_markets()
            if symbol not in binance_markets:
                logger.error(f"Symbol {symbol} not found on Binance")
                available_symbols = [s for s in binance_markets.keys() if 'SOL' in s and 'USDT' in s]
                logger.info(f"Available SOL/USDT pairs on Binance: {available_symbols}")
                return None

            binance_ticker = binance.fetch_ticker(symbol)
            prices['binance']['bid'] = binance_ticker.get('bid')
            prices['binance']['ask'] = binance_ticker.get('ask')
            prices['binance']['last'] = binance_ticker.get('last')
            prices['binance']['volume'] = binance_ticker.get('quoteVolume')
            prices['binance']['high'] = binance_ticker.get('high')
            prices['binance']['low'] = binance_ticker.get('low')

            # Skip VWAP calculation to reduce API calls and potential errors
            prices['binance']['vwap'] = None

        except Exception as e:
            logger.error(f"Error fetching Binance ticker: {e}")

        # Get Kraken ticker data
        try:
            # Load markets first to ensure the symbol exists
            kraken_markets = kraken.load_markets()
            if symbol not in kraken_markets:
                logger.error(f"Symbol {symbol} not found on Kraken")
                available_symbols = [s for s in kraken_markets.keys() if 'SOL' in s and 'USDT' in s]
                logger.info(f"Available SOL/USDT pairs on Kraken: {available_symbols}")
                return None

            kraken_ticker = kraken.fetch_ticker(symbol)
            prices['kraken']['bid'] = kraken_ticker.get('bid')
            prices['kraken']['ask'] = kraken_ticker.get('ask')
            prices['kraken']['last'] = kraken_ticker.get('last')
            prices['kraken']['volume'] = kraken_ticker.get('quoteVolume')
            prices['kraken']['high'] = kraken_ticker.get('high')
            prices['kraken']['low'] = kraken_ticker.get('low')

            # Skip VWAP calculation to reduce API calls and potential errors
            prices['kraken']['vwap'] = None

        except Exception as e:
            logger.error(f"Error fetching Kraken ticker: {e}")

        # Check if we have the minimum required data
        if (prices['binance']['bid'] is None or prices['binance']['ask'] is None or
            prices['kraken']['bid'] is None or prices['kraken']['ask'] is None):
            logger.error("Missing essential price data (bid/ask)")
            return None

        logger.info(f"Prices updated at {human_time}")
        logger.info(f"Binance: Bid={prices['binance']['bid']}, Ask={prices['binance']['ask']}")
        logger.info(f"Kraken: Bid={prices['kraken']['bid']}, Ask={prices['kraken']['ask']}")

        return prices
    except Exception as e:
        logger.error(f"Error fetching prices: {e}")
        return None

def calculate_vwap(trades):
    """Calculate Volume-Weighted Average Price from trades."""
    if not trades or len(trades) == 0:
        return None

    total_volume = 0
    total_value = 0

    for trade in trades:
        price = trade['price']
        amount = trade['amount']
        total_volume += amount
        total_value += price * amount

    if total_volume == 0:
        return None

    return total_value / total_volume

def calculate_arbitrage(prices, binance_fee=DEFAULT_BINANCE_FEE, kraken_fee=DEFAULT_KRAKEN_FEE):
    """Calculate potential arbitrage opportunities."""
    if not prices:
        return None

    # Scenario 1: Buy on Binance, sell on Kraken
    buy_binance = prices['binance']['ask']
    sell_kraken = prices['kraken']['bid']
    profit_binance_to_kraken = sell_kraken - buy_binance
    fees_binance_to_kraken = (buy_binance * binance_fee) + (sell_kraken * kraken_fee)
    net_profit_binance_to_kraken = profit_binance_to_kraken - fees_binance_to_kraken
    profit_percentage_binance_to_kraken = (net_profit_binance_to_kraken / buy_binance) * 100

    # Scenario 2: Buy on Kraken, sell on Binance
    buy_kraken = prices['kraken']['ask']
    sell_binance = prices['binance']['bid']
    profit_kraken_to_binance = sell_binance - buy_kraken
    fees_kraken_to_binance = (buy_kraken * kraken_fee) + (sell_binance * binance_fee)
    net_profit_kraken_to_binance = profit_kraken_to_binance - fees_kraken_to_binance
    profit_percentage_kraken_to_binance = (net_profit_kraken_to_binance / buy_kraken) * 100

    return {
        'binance_to_kraken': {
            'net_profit': net_profit_binance_to_kraken,
            'profit_percentage': profit_percentage_binance_to_kraken,
            'buy_price': buy_binance,
            'sell_price': sell_kraken
        },
        'kraken_to_binance': {
            'net_profit': net_profit_kraken_to_binance,
            'profit_percentage': profit_percentage_kraken_to_binance,
            'buy_price': buy_kraken,
            'sell_price': sell_binance
        }
    }

def execute_trade(exchange_buy, exchange_sell, buy_price, sell_price, amount, symbol=DEFAULT_SYMBOL):
    """Execute buy and sell orders."""
    try:
        # Place buy order
        buy_order = exchange_buy.create_limit_buy_order(symbol, amount, buy_price)
        logger.info(f"Buy order placed: {buy_order}")

        # Place sell order
        sell_order = exchange_sell.create_limit_sell_order(symbol, amount, sell_price)
        logger.info(f"Sell order placed: {sell_order}")

        return buy_order, sell_order
    except Exception as e:
        logger.error(f"Error executing trade: {e}")
        return None, None

def main(config=None, use_enhanced_strategy=True):
    """Main loop to monitor and execute arbitrage opportunities."""
    # Load configuration from .env if not provided
    if not config:
        load_dotenv()
        binance_api_key = os.getenv('BINANCE_API_KEY')
        binance_api_secret = os.getenv('BINANCE_API_SECRET')
        kraken_api_key = os.getenv('KRAKEN_API_KEY')
        kraken_api_secret = os.getenv('KRAKEN_API_SECRET')
        symbol = DEFAULT_SYMBOL
        amount = DEFAULT_AMOUNT
        min_profit_margin = DEFAULT_MIN_PROFIT_MARGIN
    else:
        binance_api_key = config.get('binance_api_key')
        binance_api_secret = config.get('binance_api_secret')
        kraken_api_key = config.get('kraken_api_key')
        kraken_api_secret = config.get('kraken_api_secret')
        symbol = config.get('symbol', DEFAULT_SYMBOL)
        amount = config.get('amount', DEFAULT_AMOUNT)
        min_profit_margin = config.get('min_profit_margin', DEFAULT_MIN_PROFIT_MARGIN) / 100  # Convert from percentage

    # Initialize exchanges
    binance, kraken = initialize_exchanges(binance_api_key, binance_api_secret, kraken_api_key, kraken_api_secret)

    # Use enhanced strategy if available and requested
    if ENHANCED_STRATEGY_AVAILABLE and use_enhanced_strategy:
        logger.info("Using enhanced arbitrage strategy")
        run_enhanced_strategy(binance, kraken, config)
        return

    # Fall back to basic strategy
    logger.info("Using basic arbitrage strategy")
    while True:
        try:
            prices = get_prices(binance, kraken, symbol)
            if not prices:
                time.sleep(5)
                continue

            opportunities = calculate_arbitrage(prices)
            if not opportunities:
                time.sleep(5)
                continue

            # Check Binance to Kraken opportunity
            if opportunities['binance_to_kraken']['net_profit'] > min_profit_margin:
                logger.info("Arbitrage opportunity: Buy on Binance, Sell on Kraken")
                buy_price = opportunities['binance_to_kraken']['buy_price']
                sell_price = opportunities['binance_to_kraken']['sell_price']
                buy_order, sell_order = execute_trade(binance, kraken, buy_price, sell_price, amount, symbol)
                if buy_order and sell_order:
                    logger.info(f"Trade executed: Bought {amount} {symbol} on Binance, Sold on Kraken")

            # Check Kraken to Binance opportunity
            elif opportunities['kraken_to_binance']['net_profit'] > min_profit_margin:
                logger.info("Arbitrage opportunity: Buy on Kraken, Sell on Binance")
                buy_price = opportunities['kraken_to_binance']['buy_price']
                sell_price = opportunities['kraken_to_binance']['sell_price']
                buy_order, sell_order = execute_trade(kraken, binance, buy_price, sell_price, amount, symbol)
                if buy_order and sell_order:
                    logger.info(f"Trade executed: Bought {amount} {symbol} on Kraken, Sold on Binance")

            else:
                logger.info("No profitable arbitrage opportunity found")

            time.sleep(5)  # Wait before next check
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            time.sleep(10)

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Cryptocurrency Arbitrage Trading Bot')
    parser.add_argument('--basic', action='store_true', help='Use basic strategy instead of enhanced')
    args = parser.parse_args()

    # Run the bot with the selected strategy
    main(use_enhanced_strategy=not args.basic)
