<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Arbitrage Bot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Cryptocurrency Arbitrage Trading Bot</h1>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <!-- Bot Control Panel -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Bot Control</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    {% if bot_running %}
                                        <a href="{{ url_for('stop_bot') }}" class="btn btn-danger">Stop Bot</a>
                                    {% else %}
                                        <a href="{{ url_for('start_bot') }}" class="btn btn-success">Start Bot</a>
                                    {% endif %}
                                </div>
                                <div class="mt-3">
                                    <h6>Status: <span id="bot-status" class="badge bg-secondary">Unknown</span></h6>
                                </div>
                            </div>
                        </div>

                        <!-- Current Prices -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Current Prices</h5>
                                <button id="refresh-prices" class="btn btn-sm btn-light" title="Refresh Prices">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="last-update" class="text-muted small mb-2">Last updated: <span id="update-time">-</span></div>
                                <div id="prices-container">
                                    <div class="mb-3">
                                        <h6 class="d-flex justify-content-between">
                                            <span>Binance</span>
                                            <span class="badge bg-secondary">Vol: <span id="binance-volume">-</span></span>
                                        </h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <p>Bid: <span id="binance-bid" class="fw-bold">-</span> <span id="binance-bid-change" class="small"></span></p>
                                                <p>Ask: <span id="binance-ask" class="fw-bold">-</span> <span id="binance-ask-change" class="small"></span></p>
                                            </div>
                                            <div class="col-6">
                                                <p>Last: <span id="binance-last">-</span></p>
                                                <p>VWAP: <span id="binance-vwap">-</span></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <p class="small">24h Range: <span id="binance-low">-</span> - <span id="binance-high">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="d-flex justify-content-between">
                                            <span>Kraken</span>
                                            <span class="badge bg-secondary">Vol: <span id="kraken-volume">-</span></span>
                                        </h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <p>Bid: <span id="kraken-bid" class="fw-bold">-</span> <span id="kraken-bid-change" class="small"></span></p>
                                                <p>Ask: <span id="kraken-ask" class="fw-bold">-</span> <span id="kraken-ask-change" class="small"></span></p>
                                            </div>
                                            <div class="col-6">
                                                <p>Last: <span id="kraken-last">-</span></p>
                                                <p>VWAP: <span id="kraken-vwap">-</span></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <p class="small">24h Range: <span id="kraken-low">-</span> - <span id="kraken-high">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Arbitrage Opportunities -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Arbitrage Opportunities</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header">Binance → Kraken</div>
                                            <div class="card-body">
                                                <p>Net Profit: <span id="binance-to-kraken-profit">-</span></p>
                                                <p>Profit %: <span id="binance-to-kraken-percentage">-</span>%</p>
                                                <p>Buy Price: <span id="binance-to-kraken-buy">-</span></p>
                                                <p>Sell Price: <span id="binance-to-kraken-sell">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header">Kraken → Binance</div>
                                            <div class="card-body">
                                                <p>Net Profit: <span id="kraken-to-binance-profit">-</span></p>
                                                <p>Profit %: <span id="kraken-to-binance-percentage">-</span>%</p>
                                                <p>Buy Price: <span id="kraken-to-binance-buy">-</span></p>
                                                <p>Sell Price: <span id="kraken-to-binance-sell">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Profit Chart -->
                                <div class="mt-3">
                                    <canvas id="profitChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuration Form -->
                <div class="card mb-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Bot Configuration</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('config') }}">
                            {{ form.csrf_token }}

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6>Binance API</h6>
                                    <div class="mb-3">
                                        <label for="binance_api_key" class="form-label">API Key</label>
                                        {{ form.binance_api_key(class="form-control", id="binance_api_key") }}
                                    </div>
                                    <div class="mb-3">
                                        <label for="binance_api_secret" class="form-label">API Secret</label>
                                        {{ form.binance_api_secret(class="form-control", id="binance_api_secret") }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Kraken API</h6>
                                    <div class="mb-3">
                                        <label for="kraken_api_key" class="form-label">API Key</label>
                                        {{ form.kraken_api_key(class="form-control", id="kraken_api_key") }}
                                    </div>
                                    <div class="mb-3">
                                        <label for="kraken_api_secret" class="form-label">API Secret</label>
                                        {{ form.kraken_api_secret(class="form-control", id="kraken_api_secret") }}
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="symbol" class="form-label">Trading Symbol</label>
                                        {{ form.symbol(class="form-control", id="symbol") }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">Trading Amount</label>
                                        {{ form.amount(class="form-control", id="amount") }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="min_profit_margin" class="form-label">Min Profit Margin (%)</label>
                                        {{ form.min_profit_margin(class="form-control", id="min_profit_margin") }}
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Strategy Options -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">Enhanced Strategy Options</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <label class="form-check-label" for="use_enhanced_strategy">
                                                    Use Enhanced Strategy
                                                    {% if enhanced_available %}
                                                    <span class="badge bg-success">Available</span>
                                                    {% else %}
                                                    <span class="badge bg-danger">Not Available</span>
                                                    {% endif %}
                                                </label>
                                                {{ form.use_enhanced_strategy(class="form-check-input", id="use_enhanced_strategy", disabled=not enhanced_available) }}
                                            </div>

                                            <div class="form-check form-switch mb-3">
                                                <label class="form-check-label" for="enable_triangular">
                                                    Enable Triangular Arbitrage
                                                </label>
                                                {{ form.enable_triangular(class="form-check-input", id="enable_triangular") }}
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_amount" class="form-label">Maximum Trading Amount</label>
                                                {{ form.max_amount(class="form-control", id="max_amount") }}
                                                <div class="form-text">Maximum amount to trade for high-profit opportunities</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="risk_factor" class="form-label">Risk Factor (0.1-2.0)</label>
                                                {{ form.risk_factor(class="form-control", id="risk_factor") }}
                                                <div class="form-text">Higher values increase trade size (more aggressive)</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="cooldown_period" class="form-label">Cooldown Period (seconds)</label>
                                                {{ form.cooldown_period(class="form-control", id="cooldown_period") }}
                                                <div class="form-text">Minimum time between trades for the same pair</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize profit chart
        const ctx = document.getElementById('profitChart').getContext('2d');
        const profitChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Binance → Kraken Profit %',
                        data: [],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        tension: 0.1,
                        fill: false
                    },
                    {
                        label: 'Kraken → Binance Profit %',
                        data: [],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        tension: 0.1,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Profit %'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                }
            }
        });

        // Store previous price data for comparison
        let previousPrices = {
            binance: { bid: null, ask: null },
            kraken: { bid: null, ask: null }
        };

        // Format number with appropriate precision
        function formatNumber(num, precision = 6) {
            if (num === null || num === undefined || isNaN(num)) return '-';
            try {
                return parseFloat(num).toFixed(precision);
            } catch (e) {
                console.warn('Error formatting number:', num, e);
                return '-';
            }
        }

        // Format large numbers (like volume) with K, M, B suffixes
        function formatLargeNumber(num) {
            if (num === null || num === undefined || isNaN(num)) return '-';
            try {
                if (num >= 1000000000) return (num / 1000000000).toFixed(2) + 'B';
                if (num >= 1000000) return (num / 1000000).toFixed(2) + 'M';
                if (num >= 1000) return (num / 1000).toFixed(2) + 'K';
                return parseFloat(num).toFixed(2);
            } catch (e) {
                console.warn('Error formatting large number:', num, e);
                return '-';
            }
        }

        // Show price change indicator
        function updatePriceChange(elementId, previousValue, currentValue, changeElementId) {
            if (previousValue === null || currentValue === null) return;

            const changeElement = document.getElementById(changeElementId);
            if (!changeElement) return;

            if (currentValue > previousValue) {
                changeElement.innerHTML = '<i class="bi bi-arrow-up-short text-success"></i>';
                changeElement.classList.add('text-success');
                changeElement.classList.remove('text-danger');
            } else if (currentValue < previousValue) {
                changeElement.innerHTML = '<i class="bi bi-arrow-down-short text-danger"></i>';
                changeElement.classList.add('text-danger');
                changeElement.classList.remove('text-success');
            } else {
                changeElement.innerHTML = '';
            }

            // Add a subtle flash effect to the price element
            const priceElement = document.getElementById(elementId);
            if (priceElement) {
                priceElement.classList.add('price-flash');
                setTimeout(() => {
                    priceElement.classList.remove('price-flash');
                }, 1000);
            }
        }

        // Update data from server
        function updateData() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    // Update bot status
                    const statusElement = document.getElementById('bot-status');
                    statusElement.textContent = data.status;

                    if (data.running) {
                        statusElement.classList.remove('bg-secondary', 'bg-danger');
                        statusElement.classList.add('bg-success');
                    } else {
                        statusElement.classList.remove('bg-secondary', 'bg-success');
                        statusElement.classList.add('bg-danger');
                    }

                    // Update prices
                    if (data.prices && Object.keys(data.prices).length > 0) {
                        try {
                            // Update timestamp
                            document.getElementById('update-time').textContent = data.prices.human_time || new Date().toLocaleString();

                            // Safely get Binance data
                            const binance = data.prices.binance || {};
                            const binanceBid = binance.bid;
                            const binanceAsk = binance.ask;

                            document.getElementById('binance-bid').textContent = formatNumber(binanceBid);
                            document.getElementById('binance-ask').textContent = formatNumber(binanceAsk);
                            document.getElementById('binance-last').textContent = formatNumber(binance.last);
                            document.getElementById('binance-vwap').textContent = formatNumber(binance.vwap);
                            document.getElementById('binance-volume').textContent = formatLargeNumber(binance.volume);
                            document.getElementById('binance-high').textContent = formatNumber(binance.high);
                            document.getElementById('binance-low').textContent = formatNumber(binance.low);

                            // Safely get Kraken data
                            const kraken = data.prices.kraken || {};
                            const krakenBid = kraken.bid;
                            const krakenAsk = kraken.ask;

                            document.getElementById('kraken-bid').textContent = formatNumber(krakenBid);
                            document.getElementById('kraken-ask').textContent = formatNumber(krakenAsk);
                            document.getElementById('kraken-last').textContent = formatNumber(kraken.last);
                            document.getElementById('kraken-vwap').textContent = formatNumber(kraken.vwap);
                            document.getElementById('kraken-volume').textContent = formatLargeNumber(kraken.volume);
                            document.getElementById('kraken-high').textContent = formatNumber(kraken.high);
                            document.getElementById('kraken-low').textContent = formatNumber(kraken.low);

                            // Only update price change indicators if we have valid data
                            if (binanceBid !== null && binanceBid !== undefined) {
                                updatePriceChange('binance-bid', previousPrices.binance.bid, binanceBid, 'binance-bid-change');
                                previousPrices.binance.bid = binanceBid;
                            }

                            if (binanceAsk !== null && binanceAsk !== undefined) {
                                updatePriceChange('binance-ask', previousPrices.binance.ask, binanceAsk, 'binance-ask-change');
                                previousPrices.binance.ask = binanceAsk;
                            }

                            if (krakenBid !== null && krakenBid !== undefined) {
                                updatePriceChange('kraken-bid', previousPrices.kraken.bid, krakenBid, 'kraken-bid-change');
                                previousPrices.kraken.bid = krakenBid;
                            }

                            if (krakenAsk !== null && krakenAsk !== undefined) {
                                updatePriceChange('kraken-ask', previousPrices.kraken.ask, krakenAsk, 'kraken-ask-change');
                                previousPrices.kraken.ask = krakenAsk;
                            }
                        } catch (error) {
                            console.error('Error updating price display:', error);
                        }
                    }

                    // Update opportunities
                    if (data.opportunities && Object.keys(data.opportunities).length > 0) {
                        try {
                            // Safely get opportunity data
                            const binanceToKraken = data.opportunities.binance_to_kraken || {};
                            const krakenToBinance = data.opportunities.kraken_to_binance || {};

                            // Binance to Kraken
                            document.getElementById('binance-to-kraken-profit').textContent =
                                formatNumber(binanceToKraken.net_profit);
                            document.getElementById('binance-to-kraken-percentage').textContent =
                                formatNumber(binanceToKraken.profit_percentage, 3);
                            document.getElementById('binance-to-kraken-buy').textContent =
                                formatNumber(binanceToKraken.buy_price);
                            document.getElementById('binance-to-kraken-sell').textContent =
                                formatNumber(binanceToKraken.sell_price);

                            // Kraken to Binance
                            document.getElementById('kraken-to-binance-profit').textContent =
                                formatNumber(krakenToBinance.net_profit);
                            document.getElementById('kraken-to-binance-percentage').textContent =
                                formatNumber(krakenToBinance.profit_percentage, 3);
                            document.getElementById('kraken-to-binance-buy').textContent =
                                formatNumber(krakenToBinance.buy_price);
                            document.getElementById('kraken-to-binance-sell').textContent =
                                formatNumber(krakenToBinance.sell_price);

                            // Only update chart if we have valid data
                            if (binanceToKraken.profit_percentage !== undefined &&
                                krakenToBinance.profit_percentage !== undefined) {
                                // Update chart
                                const now = new Date().toLocaleTimeString();
                                profitChart.data.labels.push(now);
                                profitChart.data.datasets[0].data.push(binanceToKraken.profit_percentage);
                                profitChart.data.datasets[1].data.push(krakenToBinance.profit_percentage);

                                // Keep only the last 20 data points
                                if (profitChart.data.labels.length > 20) {
                                    profitChart.data.labels.shift();
                                    profitChart.data.datasets[0].data.shift();
                                    profitChart.data.datasets[1].data.shift();
                                }

                                profitChart.update();
                            }
                        } catch (error) {
                            console.error('Error updating opportunities display:', error);
                        }
                    }
                })
                .catch(error => console.error('Error fetching status:', error));
        }

        // Manual refresh button
        document.getElementById('refresh-prices').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> Refreshing...';

            updateData();

            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            }, 1000);
        });

        // Update data every 2 seconds
        setInterval(updateData, 2000);

        // Initial update
        updateData();
    </script>
</body>
</html>
