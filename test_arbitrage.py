import ccxt
import os
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
binance_api_key = os.getenv('BINANCE_API_KEY')
binance_api_secret = os.getenv('BINANCE_API_SECRET')
kraken_api_key = os.getenv('KRAKEN_API_KEY')
kraken_api_secret = os.getenv('KRAKEN_API_SECRET')

def test_exchange_connection():
    """Test connection to exchanges and fetch basic market data."""
    try:
        # Initialize exchanges
        binance = ccxt.binance({
            'apiKey': binance_api_key,
            'secret': binance_api_secret,
            'enableRateLimit': True,
        })
        
        kraken = ccxt.kraken({
            'apiKey': kraken_api_key,
            'secret': kraken_api_secret,
            'enableRateLimit': True,
        })
        
        # Test Binance connection
        logger.info("Testing Binance connection...")
        binance_markets = binance.load_markets()
        logger.info(f"Binance connected successfully. Found {len(binance_markets)} markets.")
        
        # Test Kraken connection
        logger.info("Testing Kraken connection...")
        kraken_markets = kraken.load_markets()
        logger.info(f"Kraken connected successfully. Found {len(kraken_markets)} markets.")
        
        # Test fetching ticker data for SOL/USDT
        symbol = 'SOL/USDT'
        logger.info(f"Fetching {symbol} ticker from Binance...")
        binance_ticker = binance.fetch_ticker(symbol)
        logger.info(f"Binance {symbol} - Bid: {binance_ticker['bid']}, Ask: {binance_ticker['ask']}")
        
        logger.info(f"Fetching {symbol} ticker from Kraken...")
        kraken_ticker = kraken.fetch_ticker(symbol)
        logger.info(f"Kraken {symbol} - Bid: {kraken_ticker['bid']}, Ask: {kraken_ticker['ask']}")
        
        # Calculate potential arbitrage
        binance_ask = binance_ticker['ask']
        kraken_bid = kraken_ticker['bid']
        binance_bid = binance_ticker['bid']
        kraken_ask = kraken_ticker['ask']
        
        logger.info(f"Potential arbitrage (Buy Binance, Sell Kraken): {kraken_bid - binance_ask}")
        logger.info(f"Potential arbitrage (Buy Kraken, Sell Binance): {binance_bid - kraken_ask}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing exchange connections: {e}")
        return False

if __name__ == "__main__":
    if test_exchange_connection():
        logger.info("All tests passed successfully!")
    else:
        logger.error("Tests failed. Please check your API keys and network connection.")
