# Cryptocurrency Arbitrage Trading Bot

This bot monitors price differences between Binance and Kraken exchanges for SOL/USDT trading pair and executes trades when profitable arbitrage opportunities are detected.

## Features

### Basic Strategy
- Real-time price monitoring on Binance and Kraken
- Automatic arbitrage opportunity detection
- Configurable trading parameters
- Detailed logging of all activities
- Automatic trade execution when profitable opportunities arise
- Web-based user interface for easy configuration and monitoring
- Real-time price charts and profit visualization

### Enhanced Strategy
- **Dynamic Order Sizing**: Adjusts trade size based on opportunity size and profit potential
- **Multi-Pair Trading**: Monitors multiple trading pairs simultaneously for more opportunities
- **Order Book Analysis**: Uses order book depth for more accurate pricing and execution
- **Triangular Arbitrage**: Adds triangular arbitrage between different trading pairs
- **Smart Order Routing**: Optimizes order execution timing and method
- **Market Impact Analysis**: Accounts for market impact in larger trades
- **Historical Trend Analysis**: Uses recent price trends to predict short-term movements

## Setup

### Prerequisites

- Python 3.8 or higher
- Binance and Kraken exchange accounts with API keys
- Sufficient funds in both exchanges

### Installation

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/ArbitrageTrading.git
   cd ArbitrageTrading
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up your API keys:
   - Copy the `.env.template` file to `.env`
   - Edit the `.env` file and add your Binance and Kraken API keys and secrets

### Configuration

#### Basic Parameters
You can modify the following parameters in `arbitrage_bot.py` or through the web interface:

- `SYMBOL`: The trading pair to monitor (default: 'SOL/USDT')
- `AMOUNT`: The amount to trade (default: 1.0 SOL)
- `BINANCE_FEE`: Binance trading fee (default: 0.1%)
- `KRAKEN_FEE`: Kraken trading fee (default: 0.2%)
- `MIN_PROFIT_MARGIN`: Minimum profit margin to execute a trade (default: 0.3%)

#### Enhanced Strategy Parameters
The enhanced strategy provides additional configuration options:

- `USE_ENHANCED_STRATEGY`: Enable or disable the enhanced strategy (default: True)
- `MAX_AMOUNT`: Maximum amount to trade for high-profit opportunities (default: 5.0)
- `RISK_FACTOR`: Adjusts trade size aggressiveness (0.1-2.0, default: 1.0)
- `ENABLE_TRIANGULAR`: Enable triangular arbitrage (default: True)
- `COOLDOWN_PERIOD`: Minimum time between trades for the same pair (default: 60 seconds)

## Usage

### Command Line Interface

Run the bot in command line mode with:

```
python arbitrage_bot.py  # Uses enhanced strategy by default
```

To use the basic strategy instead:

```
python arbitrage_bot.py --basic
```

The bot will continuously monitor prices and execute trades when profitable opportunities are found.

### Web Interface

Run the web interface with:

```
python app.py
```

Then open your browser and navigate to `http://localhost:5000` to access the web interface.

The web interface allows you to:
- Configure API keys and trading parameters
- Choose between basic and enhanced trading strategies
- Configure advanced strategy parameters
- Start and stop the bot
- Monitor prices in real-time
- View potential arbitrage opportunities
- Track profit margins with interactive charts

## Logging

The bot logs all activities to the console with timestamps. You can modify the logging configuration in the script to save logs to a file if needed.

## Disclaimer

This bot is provided for educational purposes only. Use it at your own risk. Cryptocurrency trading involves significant risk and you should never trade with money you cannot afford to lose.

## License

MIT
