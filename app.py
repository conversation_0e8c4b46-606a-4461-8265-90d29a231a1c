from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from flask_wtf.csrf import CSRFProtect
from wtforms import StringField, FloatField, SubmitField, PasswordField, BooleanField, <PERSON>tegerField
from wtforms.validators import DataRequired, NumberRange
import os
import json
import threading
import time
from cryptography.fernet import Fernet
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
csrf = CSRFProtect(app)

# Global variables
bot_thread = None
bot_running = False
bot_status = "Stopped"
price_data = {}
arbitrage_opportunities = {}

# Generate encryption key if it doesn't exist
def get_encryption_key():
    key_file = "key.key"
    if os.path.exists(key_file):
        with open(key_file, "rb") as f:
            key = f.read()
    else:
        key = Fernet.generate_key()
        with open(key_file, "wb") as f:
            f.write(key)
    return key

# Initialize encryption
key = get_encryption_key()
cipher_suite = Fernet(key)

# Form for API keys and trading parameters
class ConfigForm(FlaskForm):
    binance_api_key = StringField('Binance API Key', validators=[DataRequired()])
    binance_api_secret = PasswordField('Binance API Secret', validators=[DataRequired()])
    kraken_api_key = StringField('Kraken API Key', validators=[DataRequired()])
    kraken_api_secret = PasswordField('Kraken API Secret', validators=[DataRequired()])
    symbol = StringField('Trading Symbol', validators=[DataRequired()], default='SOL/USDT')
    amount = FloatField('Trading Amount', validators=[DataRequired(), NumberRange(min=0.01)], default=1.0)
    min_profit_margin = FloatField('Minimum Profit Margin (%)', validators=[DataRequired(), NumberRange(min=0.1)], default=0.3)
    use_enhanced_strategy = BooleanField('Use Enhanced Strategy', default=True)
    max_amount = FloatField('Maximum Trading Amount', validators=[NumberRange(min=0.01)], default=5.0)
    risk_factor = FloatField('Risk Factor (0.1-2.0)', validators=[NumberRange(min=0.1, max=2.0)], default=1.0)
    enable_triangular = BooleanField('Enable Triangular Arbitrage', default=True)
    cooldown_period = IntegerField('Cooldown Period (seconds)', validators=[NumberRange(min=0)], default=60)
    submit = SubmitField('Save Configuration')

# Load configuration
def load_config():
    if os.path.exists('config.json'):
        try:
            with open('config.json', 'r') as f:
                encrypted_data = json.load(f)

            # Decrypt the data
            decrypted_data = {}
            for key, value in encrypted_data.items():
                if key in ['binance_api_key', 'binance_api_secret', 'kraken_api_key', 'kraken_api_secret']:
                    decrypted_data[key] = cipher_suite.decrypt(value.encode()).decode()
                else:
                    decrypted_data[key] = value

            return decrypted_data
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {
                'binance_api_key': '',
                'binance_api_secret': '',
                'kraken_api_key': '',
                'kraken_api_secret': '',
                'symbol': 'SOL/USDT',
                'amount': 1.0,
                'min_profit_margin': 0.3,
                'use_enhanced_strategy': True,
                'max_amount': 5.0,
                'risk_factor': 1.0,
                'enable_triangular': True,
                'cooldown_period': 60
            }
    else:
        return {
            'binance_api_key': '',
            'binance_api_secret': '',
            'kraken_api_key': '',
            'kraken_api_secret': '',
            'symbol': 'SOL/USDT',
            'amount': 1.0,
            'min_profit_margin': 0.3,
            'use_enhanced_strategy': True,
            'max_amount': 5.0,
            'risk_factor': 1.0,
            'enable_triangular': True,
            'cooldown_period': 60
        }

# Save configuration
def save_config(config_data):
    # Encrypt sensitive data
    encrypted_data = {}
    for key, value in config_data.items():
        if key in ['binance_api_key', 'binance_api_secret', 'kraken_api_key', 'kraken_api_secret']:
            encrypted_data[key] = cipher_suite.encrypt(value.encode()).decode()
        else:
            encrypted_data[key] = value

    with open('config.json', 'w') as f:
        json.dump(encrypted_data, f)

# Import the arbitrage bot functions
from arbitrage_bot import get_prices, calculate_arbitrage, execute_trade, main as run_basic_strategy

# Try to import enhanced strategy
try:
    from enhanced_strategy import run_enhanced_strategy
    ENHANCED_STRATEGY_AVAILABLE = True
except ImportError:
    logger.warning("Enhanced strategy module not found. Using basic strategy.")
    ENHANCED_STRATEGY_AVAILABLE = False

# Function to run the bot in a separate thread
def run_bot():
    global bot_running, bot_status, price_data, arbitrage_opportunities

    config = load_config()

    # Import here to avoid circular imports
    import ccxt

    # Initialize exchanges
    binance = ccxt.binance({
        'apiKey': config['binance_api_key'],
        'secret': config['binance_api_secret'],
        'enableRateLimit': True,
    })

    kraken = ccxt.kraken({
        'apiKey': config['kraken_api_key'],
        'secret': config['kraken_api_secret'],
        'enableRateLimit': True,
    })

    # Check if we should use enhanced strategy
    use_enhanced = config.get('use_enhanced_strategy', True) and ENHANCED_STRATEGY_AVAILABLE

    if use_enhanced:
        # Use the enhanced strategy
        bot_status = "Running with Enhanced Strategy"
        logger.info("Starting bot with enhanced strategy")

        # Create a thread-safe way to update status
        def status_callback(status_message):
            global bot_status
            bot_status = status_message

        # Run in monitoring mode (no actual trades)
        config['monitor_only'] = True

        try:
            # Start the enhanced strategy in a separate thread
            from threading import Thread
            strategy_thread = Thread(target=run_enhanced_strategy, args=(binance, kraken, config))
            strategy_thread.daemon = True
            strategy_thread.start()

            # Monitor the strategy thread and update status
            while bot_running and strategy_thread.is_alive():
                # Update price data for UI
                try:
                    symbol = config['symbol']
                    prices = get_prices(binance, kraken, symbol)
                    if prices:
                        price_data = prices

                        # Calculate basic arbitrage for UI display
                        opportunities = calculate_arbitrage(prices, binance_fee=0.001, kraken_fee=0.002)
                        if opportunities:
                            arbitrage_opportunities = opportunities
                except Exception as e:
                    logger.error(f"Error updating UI data: {e}")

                time.sleep(2)

            if not strategy_thread.is_alive():
                bot_status = "Enhanced strategy stopped unexpectedly"
                logger.error(bot_status)
        except Exception as e:
            bot_status = f"Error in enhanced strategy: {str(e)}"
            logger.error(bot_status)
    else:
        # Use the basic strategy
        bot_status = "Running with Basic Strategy"
        logger.info("Starting bot with basic strategy")

        symbol = config['symbol']
        amount = config['amount']
        min_profit_margin = config['min_profit_margin'] / 100  # Convert from percentage

        while bot_running:
            try:
                # Get prices
                prices = get_prices(binance, kraken, symbol)
                if prices:
                    price_data = prices

                    # Calculate arbitrage opportunities
                    opportunities = calculate_arbitrage(prices, binance_fee=0.001, kraken_fee=0.002)
                    if opportunities:
                        arbitrage_opportunities = opportunities

                        # Check for profitable opportunities
                        if opportunities['binance_to_kraken']['net_profit'] > min_profit_margin:
                            bot_status = "Arbitrage opportunity found: Buy on Binance, Sell on Kraken"
                            logger.info(bot_status)

                            # In real trading mode, uncomment this:
                            # buy_price = opportunities['binance_to_kraken']['buy_price']
                            # sell_price = opportunities['binance_to_kraken']['sell_price']
                            # execute_trade(binance, kraken, buy_price, sell_price, amount, symbol)

                        elif opportunities['kraken_to_binance']['net_profit'] > min_profit_margin:
                            bot_status = "Arbitrage opportunity found: Buy on Kraken, Sell on Binance"
                            logger.info(bot_status)

                            # In real trading mode, uncomment this:
                            # buy_price = opportunities['kraken_to_binance']['buy_price']
                            # sell_price = opportunities['kraken_to_binance']['sell_price']
                            # execute_trade(kraken, binance, buy_price, sell_price, amount, symbol)

                        else:
                            bot_status = "Monitoring prices - No profitable opportunity found"
                    else:
                        bot_status = "Error calculating arbitrage opportunities"
                else:
                    bot_status = "Error fetching prices"

                time.sleep(2)  # Check every 2 seconds for more frequent updates

            except Exception as e:
                bot_status = f"Error in bot: {str(e)}"
                logger.error(bot_status)
                time.sleep(10)

    bot_status = "Stopped"

@app.route('/')
def index():
    config = load_config()
    form = ConfigForm(
        binance_api_key=config['binance_api_key'],
        binance_api_secret=config['binance_api_secret'],
        kraken_api_key=config['kraken_api_key'],
        kraken_api_secret=config['kraken_api_secret'],
        symbol=config['symbol'],
        amount=config['amount'],
        min_profit_margin=config['min_profit_margin'],
        use_enhanced_strategy=config.get('use_enhanced_strategy', True),
        max_amount=config.get('max_amount', 5.0),
        risk_factor=config.get('risk_factor', 1.0),
        enable_triangular=config.get('enable_triangular', True),
        cooldown_period=config.get('cooldown_period', 60)
    )
    return render_template('index.html', form=form, bot_running=bot_running, enhanced_available=ENHANCED_STRATEGY_AVAILABLE)

@app.route('/config', methods=['POST'])
def config():
    form = ConfigForm()
    if form.validate_on_submit():
        config_data = {
            'binance_api_key': form.binance_api_key.data,
            'binance_api_secret': form.binance_api_secret.data,
            'kraken_api_key': form.kraken_api_key.data,
            'kraken_api_secret': form.kraken_api_secret.data,
            'symbol': form.symbol.data,
            'amount': form.amount.data,
            'min_profit_margin': form.min_profit_margin.data,
            'use_enhanced_strategy': form.use_enhanced_strategy.data,
            'max_amount': form.max_amount.data,
            'risk_factor': form.risk_factor.data,
            'enable_triangular': form.enable_triangular.data,
            'cooldown_period': form.cooldown_period.data
        }
        save_config(config_data)
        flash('Configuration saved successfully!', 'success')
        return redirect(url_for('index'))

    flash('There was an error with your form submission.', 'danger')
    return redirect(url_for('index'))

@app.route('/start_bot')
def start_bot():
    global bot_thread, bot_running

    if not bot_running:
        bot_running = True
        bot_thread = threading.Thread(target=run_bot)
        bot_thread.daemon = True
        bot_thread.start()
        flash('Bot started successfully!', 'success')
    else:
        flash('Bot is already running!', 'warning')

    return redirect(url_for('index'))

@app.route('/stop_bot')
def stop_bot():
    global bot_running

    if bot_running:
        bot_running = False
        flash('Bot stopped successfully!', 'success')
    else:
        flash('Bot is not running!', 'warning')

    return redirect(url_for('index'))

@app.route('/status')
def status():
    return jsonify({
        'status': bot_status,
        'running': bot_running,
        'prices': price_data,
        'opportunities': arbitrage_opportunities
    })

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')

    # Create static directory if it doesn't exist
    if not os.path.exists('static'):
        os.makedirs('static')

    app.run(debug=True)
