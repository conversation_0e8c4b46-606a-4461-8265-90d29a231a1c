import ccxt
import os
import time
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from collections import deque

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default trading parameters
DEFAULT_BINANCE_FEE = 0.001  # 0.1% trading fee
DEFAULT_KRAKEN_FEE = 0.002   # 0.2% trading fee
DEFAULT_MIN_PROFIT_MARGIN = 0.003  # 0.3% minimum profit margin

# Trading pairs to monitor
DEFAULT_PAIRS = [
    'SOL/USDT',  # Primary pair
    'BTC/USDT',  # High liquidity pairs
    'ETH/USDT',
    'BNB/USDT',
    'ADA/USDT'
]

# Historical price data storage (for trend analysis)
price_history = {pair: deque(maxlen=100) for pair in DEFAULT_PAIRS}

# Order book cache
order_book_cache = {}

class EnhancedStrategy:
    """Enhanced arbitrage strategy with multiple profit-boosting techniques."""
    
    def __init__(self, binance, kraken, config=None):
        """Initialize the enhanced strategy."""
        self.binance = binance
        self.kraken = kraken
        self.config = config or {}
        self.min_profit_margin = self.config.get('min_profit_margin', DEFAULT_MIN_PROFIT_MARGIN) / 100
        self.base_amount = float(self.config.get('amount', 1.0))
        self.pairs = self.config.get('pairs', DEFAULT_PAIRS)
        self.binance_fee = self.config.get('binance_fee', DEFAULT_BINANCE_FEE)
        self.kraken_fee = self.config.get('kraken_fee', DEFAULT_KRAKEN_FEE)
        self.max_amount = float(self.config.get('max_amount', self.base_amount * 5))
        self.risk_factor = float(self.config.get('risk_factor', 1.0))
        self.enable_triangular = self.config.get('enable_triangular', True)
        self.last_trade_time = {pair: 0 for pair in self.pairs}
        self.cooldown_period = int(self.config.get('cooldown_period', 60))  # seconds
        self.trade_history = []
        
        # Initialize price history
        self.initialize_price_history()
        
    def initialize_price_history(self):
        """Initialize price history for trend analysis."""
        logger.info("Initializing price history for trend analysis...")
        
        for pair in self.pairs:
            try:
                # Get recent candles from Binance (more reliable historical data)
                candles = self.binance.fetch_ohlcv(pair, '1m', limit=50)
                
                # Convert to price points (closing prices)
                for candle in candles:
                    timestamp, _, _, _, close, _ = candle
                    price_history[pair].append({
                        'timestamp': timestamp,
                        'price': close
                    })
                
                logger.info(f"Initialized price history for {pair} with {len(price_history[pair])} data points")
            except Exception as e:
                logger.error(f"Error initializing price history for {pair}: {e}")
    
    def update_price_history(self, pair, price_data):
        """Update price history with new data."""
        if pair in price_history:
            price_history[pair].append({
                'timestamp': int(time.time() * 1000),
                'price': (price_data['binance']['last'] + price_data['kraken']['last']) / 2
            })
    
    def analyze_trend(self, pair):
        """Analyze recent price trend to predict short-term movement."""
        if pair not in price_history or len(price_history[pair]) < 10:
            return {'trend': 'neutral', 'strength': 0}
        
        # Convert to pandas DataFrame for easier analysis
        df = pd.DataFrame(list(price_history[pair]))
        
        # Calculate short and long moving averages
        df['short_ma'] = df['price'].rolling(window=5).mean()
        df['long_ma'] = df['price'].rolling(window=20).mean()
        
        # Calculate RSI
        delta = df['price'].diff()
        gain = delta.where(delta > 0, 0).fillna(0)
        loss = -delta.where(delta < 0, 0).fillna(0)
        avg_gain = gain.rolling(window=14).mean()
        avg_loss = loss.rolling(window=14).mean()
        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Get latest values
        latest = df.iloc[-1]
        
        # Determine trend
        if latest['short_ma'] > latest['long_ma']:
            trend = 'bullish'
            strength = min((latest['short_ma'] / latest['long_ma'] - 1) * 100, 1)
        elif latest['short_ma'] < latest['long_ma']:
            trend = 'bearish'
            strength = min((1 - latest['short_ma'] / latest['long_ma']) * 100, 1)
        else:
            trend = 'neutral'
            strength = 0
        
        # Adjust based on RSI
        if not np.isnan(latest['rsi']):
            if latest['rsi'] > 70:
                trend = 'overbought'
                strength = min((latest['rsi'] - 70) / 30, 1)
            elif latest['rsi'] < 30:
                trend = 'oversold'
                strength = min((30 - latest['rsi']) / 30, 1)
        
        return {'trend': trend, 'strength': strength}
    
    def get_order_book_depth(self, exchange, pair, limit=10):
        """Get order book with depth analysis."""
        cache_key = f"{exchange.id}_{pair}"
        
        # Check if we have a recent cache (less than 5 seconds old)
        if cache_key in order_book_cache:
            cache_time, order_book = order_book_cache[cache_key]
            if time.time() - cache_time < 5:
                return order_book
        
        try:
            order_book = exchange.fetch_order_book(pair, limit=limit)
            
            # Calculate additional metrics
            bid_volume = sum(bid[1] for bid in order_book['bids'])
            ask_volume = sum(ask[1] for ask in order_book['asks'])
            
            # Add volume imbalance metric
            if bid_volume + ask_volume > 0:
                order_book['bid_ask_ratio'] = bid_volume / (bid_volume + ask_volume)
            else:
                order_book['bid_ask_ratio'] = 0.5
            
            # Add weighted average prices
            if bid_volume > 0:
                order_book['weighted_bid'] = sum(bid[0] * bid[1] for bid in order_book['bids']) / bid_volume
            else:
                order_book['weighted_bid'] = order_book['bids'][0][0] if order_book['bids'] else None
                
            if ask_volume > 0:
                order_book['weighted_ask'] = sum(ask[0] * ask[1] for ask in order_book['asks']) / ask_volume
            else:
                order_book['weighted_ask'] = order_book['asks'][0][0] if order_book['asks'] else None
            
            # Cache the result
            order_book_cache[cache_key] = (time.time(), order_book)
            
            return order_book
        except Exception as e:
            logger.error(f"Error fetching order book for {pair} on {exchange.id}: {e}")
            return None
    
    def calculate_dynamic_amount(self, pair, profit_percentage, base_price):
        """Calculate dynamic trade amount based on opportunity size."""
        # Start with base amount
        amount = self.base_amount
        
        # Scale up based on profit percentage (more profit = larger trade)
        profit_scale = min(profit_percentage / self.min_profit_margin, 5)
        amount *= (1 + (profit_scale - 1) * 0.5)
        
        # Adjust based on trend analysis
        trend_data = self.analyze_trend(pair)
        if trend_data['trend'] in ['bullish', 'oversold']:
            # Increase amount for favorable trends
            amount *= (1 + trend_data['strength'] * 0.2)
        elif trend_data['trend'] in ['bearish', 'overbought']:
            # Decrease amount for unfavorable trends
            amount *= (1 - trend_data['strength'] * 0.2)
        
        # Apply risk factor
        amount *= self.risk_factor
        
        # Ensure we don't exceed max amount
        amount = min(amount, self.max_amount)
        
        # Round to appropriate precision based on price
        precision = 3
        if base_price < 1:
            precision = 1
        elif base_price < 10:
            precision = 2
        elif base_price < 100:
            precision = 3
        else:
            precision = 4
            
        return round(amount, precision)
    
    def calculate_direct_arbitrage(self, pair):
        """Calculate direct arbitrage opportunities between exchanges."""
        # Get prices from both exchanges
        try:
            binance_ticker = self.binance.fetch_ticker(pair)
            kraken_ticker = self.kraken.fetch_ticker(pair)
            
            # Get order book data for more accurate pricing
            binance_book = self.get_order_book_depth(self.binance, pair)
            kraken_book = self.get_order_book_depth(self.kraken, pair)
            
            if not binance_book or not kraken_book:
                return None
                
            # Use weighted prices from order book when available
            binance_bid = binance_book.get('weighted_bid', binance_ticker['bid'])
            binance_ask = binance_book.get('weighted_ask', binance_ticker['ask'])
            kraken_bid = kraken_book.get('weighted_bid', kraken_ticker['bid'])
            kraken_ask = kraken_book.get('weighted_ask', kraken_ticker['ask'])
            
            # Prepare price data structure
            prices = {
                'timestamp': int(time.time() * 1000),
                'human_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'binance': {
                    'bid': binance_bid,
                    'ask': binance_ask,
                    'last': binance_ticker['last'],
                    'volume': binance_ticker.get('quoteVolume', 0),
                    'bid_ask_ratio': binance_book.get('bid_ask_ratio', 0.5)
                },
                'kraken': {
                    'bid': kraken_bid,
                    'ask': kraken_ask,
                    'last': kraken_ticker['last'],
                    'volume': kraken_ticker.get('quoteVolume', 0),
                    'bid_ask_ratio': kraken_book.get('bid_ask_ratio', 0.5)
                }
            }
            
            # Update price history
            self.update_price_history(pair, prices)
            
            # Scenario 1: Buy on Binance, sell on Kraken
            buy_binance = prices['binance']['ask']
            sell_kraken = prices['kraken']['bid']
            profit_binance_to_kraken = sell_kraken - buy_binance
            fees_binance_to_kraken = (buy_binance * self.binance_fee) + (sell_kraken * self.kraken_fee)
            net_profit_binance_to_kraken = profit_binance_to_kraken - fees_binance_to_kraken
            profit_percentage_binance_to_kraken = (net_profit_binance_to_kraken / buy_binance) * 100
            
            # Scenario 2: Buy on Kraken, sell on Binance
            buy_kraken = prices['kraken']['ask']
            sell_binance = prices['binance']['bid']
            profit_kraken_to_binance = sell_binance - buy_kraken
            fees_kraken_to_binance = (buy_kraken * self.kraken_fee) + (sell_binance * self.binance_fee)
            net_profit_kraken_to_binance = profit_kraken_to_binance - fees_kraken_to_binance
            profit_percentage_kraken_to_binance = (net_profit_kraken_to_binance / buy_kraken) * 100
            
            # Calculate dynamic amounts
            amount_binance_to_kraken = self.calculate_dynamic_amount(
                pair, profit_percentage_binance_to_kraken, buy_binance)
            amount_kraken_to_binance = self.calculate_dynamic_amount(
                pair, profit_percentage_kraken_to_binance, buy_kraken)
            
            # Adjust for market impact
            market_impact_binance = self.estimate_market_impact(binance_book, amount_binance_to_kraken, 'buy')
            market_impact_kraken = self.estimate_market_impact(kraken_book, amount_binance_to_kraken, 'sell')
            adjusted_profit_binance_to_kraken = profit_percentage_binance_to_kraken - market_impact_binance - market_impact_kraken
            
            market_impact_kraken_buy = self.estimate_market_impact(kraken_book, amount_kraken_to_binance, 'buy')
            market_impact_binance_sell = self.estimate_market_impact(binance_book, amount_kraken_to_binance, 'sell')
            adjusted_profit_kraken_to_binance = profit_percentage_kraken_to_binance - market_impact_kraken_buy - market_impact_binance_sell
            
            return {
                'pair': pair,
                'prices': prices,
                'binance_to_kraken': {
                    'net_profit': net_profit_binance_to_kraken,
                    'profit_percentage': profit_percentage_binance_to_kraken,
                    'adjusted_profit_percentage': adjusted_profit_binance_to_kraken,
                    'buy_price': buy_binance,
                    'sell_price': sell_kraken,
                    'amount': amount_binance_to_kraken,
                    'market_impact': market_impact_binance + market_impact_kraken
                },
                'kraken_to_binance': {
                    'net_profit': net_profit_kraken_to_binance,
                    'profit_percentage': profit_percentage_kraken_to_binance,
                    'adjusted_profit_percentage': adjusted_profit_kraken_to_binance,
                    'buy_price': buy_kraken,
                    'sell_price': sell_binance,
                    'amount': amount_kraken_to_binance,
                    'market_impact': market_impact_kraken_buy + market_impact_binance_sell
                }
            }
        except Exception as e:
            logger.error(f"Error calculating direct arbitrage for {pair}: {e}")
            return None
    
    def estimate_market_impact(self, order_book, amount, side):
        """Estimate market impact of a trade."""
        if not order_book:
            return 0.1  # Default impact if we can't calculate
            
        try:
            if side == 'buy':
                orders = order_book['asks']
            else:
                orders = order_book['bids']
                
            if not orders:
                return 0.1
                
            # Calculate how much of the order book we'd consume
            total_volume = sum(order[1] for order in orders)
            
            if total_volume == 0:
                return 0.1
                
            # Impact is proportional to the percentage of the order book we'd consume
            impact_percentage = min(amount / total_volume * 100, 10)  # Cap at 10%
            
            return impact_percentage * 0.01  # Convert to percentage points
        except Exception as e:
            logger.error(f"Error estimating market impact: {e}")
            return 0.1
    
    def calculate_triangular_arbitrage(self):
        """Calculate triangular arbitrage opportunities."""
        if not self.enable_triangular:
            return None
            
        triangular_opportunities = []
        
        # Define triangular paths to check
        triangular_paths = [
            # Path: USDT -> SOL -> BTC -> USDT
            ['SOL/USDT', 'SOL/BTC', 'BTC/USDT'],
            # Path: USDT -> ETH -> SOL -> USDT
            ['ETH/USDT', 'ETH/SOL', 'SOL/USDT'],
            # Path: USDT -> BNB -> BTC -> USDT
            ['BNB/USDT', 'BNB/BTC', 'BTC/USDT']
        ]
        
        for path in triangular_paths:
            try:
                # Check if all pairs in the path are available
                if not all(self.binance.has_symbol(pair) for pair in path):
                    continue
                    
                # Get tickers for all pairs
                tickers = {}
                for pair in path:
                    ticker = self.binance.fetch_ticker(pair)
                    tickers[pair] = ticker
                
                # Calculate triangular arbitrage
                # Start with 1 USDT
                initial_amount = 1.0
                current_amount = initial_amount
                
                # Simulate the trades
                for i, pair in enumerate(path):
                    ticker = tickers[pair]
                    
                    # Extract the base and quote currencies
                    base, quote = pair.split('/')
                    
                    # Determine if we're buying or selling the base currency
                    if i == 0:
                        # First trade: Buy using USDT
                        rate = ticker['ask']  # We're buying, so use ask price
                        current_amount = (current_amount / rate) * (1 - self.binance_fee)
                    elif i == len(path) - 1:
                        # Last trade: Sell back to USDT
                        rate = ticker['bid']  # We're selling, so use bid price
                        current_amount = (current_amount * rate) * (1 - self.binance_fee)
                    else:
                        # Middle trade: Depends on the pair
                        if quote == path[i-1].split('/')[0]:
                            # We're buying the base currency
                            rate = ticker['ask']
                            current_amount = (current_amount / rate) * (1 - self.binance_fee)
                        else:
                            # We're selling the base currency
                            rate = ticker['bid']
                            current_amount = (current_amount * rate) * (1 - self.binance_fee)
                
                # Calculate profit
                profit = current_amount - initial_amount
                profit_percentage = (profit / initial_amount) * 100
                
                if profit_percentage > self.min_profit_margin * 100:
                    triangular_opportunities.append({
                        'path': path,
                        'profit': profit,
                        'profit_percentage': profit_percentage,
                        'tickers': tickers
                    })
            except Exception as e:
                logger.error(f"Error calculating triangular arbitrage for path {path}: {e}")
        
        return triangular_opportunities if triangular_opportunities else None
    
    def execute_direct_arbitrage(self, opportunity):
        """Execute a direct arbitrage trade."""
        pair = opportunity['pair']
        
        # Check if we're in cooldown period for this pair
        current_time = time.time()
        if current_time - self.last_trade_time.get(pair, 0) < self.cooldown_period:
            logger.info(f"Skipping trade for {pair} - in cooldown period")
            return False
        
        # Determine which direction has better profit
        binance_to_kraken = opportunity['binance_to_kraken']
        kraken_to_binance = opportunity['kraken_to_binance']
        
        # Use adjusted profit that accounts for market impact
        if binance_to_kraken['adjusted_profit_percentage'] > kraken_to_binance['adjusted_profit_percentage']:
            direction = 'binance_to_kraken'
            exchange_buy = self.binance
            exchange_sell = self.kraken
            buy_price = binance_to_kraken['buy_price']
            sell_price = binance_to_kraken['sell_price']
            amount = binance_to_kraken['amount']
            profit_percentage = binance_to_kraken['adjusted_profit_percentage']
        else:
            direction = 'kraken_to_binance'
            exchange_buy = self.kraken
            exchange_sell = self.binance
            buy_price = kraken_to_binance['buy_price']
            sell_price = kraken_to_binance['sell_price']
            amount = kraken_to_binance['amount']
            profit_percentage = kraken_to_binance['adjusted_profit_percentage']
        
        # Check if profit meets minimum threshold
        if profit_percentage < self.min_profit_margin * 100:
            logger.info(f"Profit for {pair} ({profit_percentage:.3f}%) below minimum threshold ({self.min_profit_margin * 100:.3f}%)")
            return False
        
        try:
            logger.info(f"Executing arbitrage: {direction} for {pair} with {amount} units, expected profit: {profit_percentage:.3f}%")
            
            # Place buy order
            buy_order = exchange_buy.create_limit_buy_order(pair, amount, buy_price)
            logger.info(f"Buy order placed: {buy_order}")
            
            # Place sell order
            sell_order = exchange_sell.create_limit_sell_order(pair, amount, sell_price)
            logger.info(f"Sell order placed: {sell_order}")
            
            # Record the trade
            trade_record = {
                'timestamp': int(time.time() * 1000),
                'human_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'pair': pair,
                'direction': direction,
                'amount': amount,
                'buy_price': buy_price,
                'sell_price': sell_price,
                'expected_profit_percentage': profit_percentage,
                'buy_order_id': buy_order.get('id'),
                'sell_order_id': sell_order.get('id')
            }
            self.trade_history.append(trade_record)
            
            # Update last trade time
            self.last_trade_time[pair] = current_time
            
            return True
        except Exception as e:
            logger.error(f"Error executing arbitrage trade for {pair}: {e}")
            return False
    
    def execute_triangular_arbitrage(self, opportunity):
        """Execute a triangular arbitrage trade."""
        path = opportunity['path']
        profit_percentage = opportunity['profit_percentage']
        
        logger.info(f"Executing triangular arbitrage: {' -> '.join(path)}, expected profit: {profit_percentage:.3f}%")
        
        try:
            # Start with a fixed amount of USDT
            initial_amount = self.base_amount
            current_amount = initial_amount
            current_currency = 'USDT'
            
            orders = []
            
            # Execute each trade in the path
            for i, pair in enumerate(path):
                ticker = opportunity['tickers'][pair]
                base, quote = pair.split('/')
                
                # Determine if we're buying or selling the base currency
                if quote == current_currency:
                    # We're buying the base currency
                    side = 'buy'
                    price = ticker['ask'] * 1.005  # Add 0.5% buffer to ensure execution
                    amount = current_amount / price
                    current_amount = amount * (1 - self.binance_fee)
                    current_currency = base
                else:
                    # We're selling the base currency
                    side = 'sell'
                    price = ticker['bid'] * 0.995  # Subtract 0.5% buffer to ensure execution
                    amount = current_amount
                    current_amount = amount * price * (1 - self.binance_fee)
                    current_currency = quote
                
                # Place the order
                if side == 'buy':
                    order = self.binance.create_limit_buy_order(pair, amount, price)
                else:
                    order = self.binance.create_limit_sell_order(pair, amount, price)
                
                orders.append(order)
                logger.info(f"Placed {side} order for {pair}: {order}")
            
            # Record the triangular trade
            trade_record = {
                'timestamp': int(time.time() * 1000),
                'human_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'type': 'triangular',
                'path': path,
                'initial_amount': initial_amount,
                'final_amount': current_amount,
                'profit_percentage': profit_percentage,
                'orders': [order.get('id') for order in orders]
            }
            self.trade_history.append(trade_record)
            
            return True
        except Exception as e:
            logger.error(f"Error executing triangular arbitrage: {e}")
            return False
    
    def run(self):
        """Run the enhanced strategy."""
        logger.info("Starting enhanced arbitrage strategy...")
        
        while True:
            try:
                # Check direct arbitrage opportunities for all pairs
                direct_opportunities = []
                for pair in self.pairs:
                    opportunity = self.calculate_direct_arbitrage(pair)
                    if opportunity:
                        direct_opportunities.append(opportunity)
                
                # Check triangular arbitrage opportunities
                triangular_opportunities = self.calculate_triangular_arbitrage()
                
                # Execute the most profitable opportunity
                if direct_opportunities or triangular_opportunities:
                    # Find the most profitable direct opportunity
                    best_direct = None
                    best_direct_profit = 0
                    
                    for opp in direct_opportunities:
                        binance_to_kraken = opp['binance_to_kraken']['adjusted_profit_percentage']
                        kraken_to_binance = opp['kraken_to_binance']['adjusted_profit_percentage']
                        best_profit = max(binance_to_kraken, kraken_to_binance)
                        
                        if best_profit > best_direct_profit:
                            best_direct_profit = best_profit
                            best_direct = opp
                    
                    # Find the most profitable triangular opportunity
                    best_triangular = None
                    best_triangular_profit = 0
                    
                    if triangular_opportunities:
                        for opp in triangular_opportunities:
                            if opp['profit_percentage'] > best_triangular_profit:
                                best_triangular_profit = opp['profit_percentage']
                                best_triangular = opp
                    
                    # Compare and execute the best opportunity
                    if best_direct and best_triangular:
                        if best_direct_profit > best_triangular_profit:
                            self.execute_direct_arbitrage(best_direct)
                        else:
                            self.execute_triangular_arbitrage(best_triangular)
                    elif best_direct:
                        self.execute_direct_arbitrage(best_direct)
                    elif best_triangular:
                        self.execute_triangular_arbitrage(best_triangular)
                else:
                    logger.info("No profitable arbitrage opportunities found")
                
                # Wait before next check
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"Error in enhanced strategy: {e}")
                time.sleep(10)

# Function to run the enhanced strategy
def run_enhanced_strategy(binance, kraken, config=None):
    """Run the enhanced arbitrage strategy."""
    strategy = EnhancedStrategy(binance, kraken, config)
    strategy.run()
